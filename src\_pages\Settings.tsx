import { useState, useRef, useEffect } from "react"

export default function Settings() {
  const [apiKey, setApiKey] = useState<string>("")
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<string>("")
  const [messageType, setMessageType] = useState<"success" | "error" | "">("")
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        window.electronAPI.updateContentDimensions({
          width: 500,
          height: 400
        })
      }
    }

    updateDimensions()
    loadApiKey()
  }, [])

  const loadApiKey = async () => {
    try {
      const storedKey = await window.electronAPI.getGeminiApiKey()
      if (storedKey) {
        setApiKey(storedKey)
      } else {
        // Set default API key if none is stored
        setApiKey("AIzaSyCHx-QiAC3j2euLc_hmw9QhsVJwC6NXVzo")
      }
    } catch (error) {
      console.error("Error loading API key:", error)
      // Set default API key on error too
      setApiKey("AIzaSyCHx-QiAC3j2euLc_hmw9QhsVJwC6NXVzo")
    }
  }

  const handleSave = async () => {
    if (!apiKey.trim()) {
      setMessage("Please enter a valid API key")
      setMessageType("error")
      setTimeout(() => setMessage(""), 3000)
      return
    }

    setIsLoading(true)
    try {
      const result = await window.electronAPI.setGeminiApiKey(apiKey.trim())
      if (result.success) {
        setMessage("API key saved successfully!")
        setMessageType("success")
      } else {
        setMessage(result.error || "Failed to save API key")
        setMessageType("error")
      }
    } catch (error) {
      console.error("Error saving API key:", error)
      setMessage("Failed to save API key")
      setMessageType("error")
    } finally {
      setIsLoading(false)
      setTimeout(() => setMessage(""), 3000)
    }
  }

  const handleTestKey = async () => {
    if (!apiKey.trim()) {
      setMessage("Please enter an API key first")
      setMessageType("error")
      setTimeout(() => setMessage(""), 3000)
      return
    }

    setIsLoading(true)
    try {
      const result = await window.electronAPI.testGeminiApiKey(apiKey.trim())
      if (result.success) {
        setMessage("API key is valid!")
        setMessageType("success")
      } else {
        setMessage(result.error || "API key is invalid")
        setMessageType("error")
      }
    } catch (error) {
      console.error("Error testing API key:", error)
      setMessage("Failed to test API key")
      setMessageType("error")
    } finally {
      setIsLoading(false)
      setTimeout(() => setMessage(""), 3000)
    }
  }

  return (
    <div
      ref={containerRef}
      className="h-[400px] w-[500px] bg-black flex items-center justify-center"
    >
      <div className="w-full px-8">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-white mb-2">
            Gemini API Settings
          </h2>
          <p className="text-gray-400 text-sm">
            Configure your Google Gemini API key to use Interview Coder for free
          </p>
        </div>

        <div className="space-y-6">
          <div>
            <label className="block text-white text-sm font-medium mb-2">
              Gemini API Key
            </label>
            <input
              type="password"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="Enter your Gemini API key"
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
              disabled={isLoading}
            />
            <p className="text-gray-500 text-xs mt-1">
              Get your free API key from{" "}
              <button
                onClick={() => window.electronAPI.openExternalUrl("https://aistudio.google.com/app/apikey")}
                className="text-blue-400 hover:text-blue-300 underline"
              >
                Google AI Studio
              </button>
            </p>
          </div>

          <div className="flex space-x-3">
            <button
              onClick={handleTestKey}
              disabled={isLoading || !apiKey.trim()}
              className="flex-1 px-4 py-2 bg-gray-700 text-white rounded-lg font-medium hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? "Testing..." : "Test Key"}
            </button>
            <button
              onClick={handleSave}
              disabled={isLoading || !apiKey.trim()}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? "Saving..." : "Save"}
            </button>
          </div>

          {message && (
            <div
              className={`text-center text-sm ${
                messageType === "success" ? "text-green-400" : "text-red-400"
              }`}
            >
              {message}
            </div>
          )}

          <div className="text-center">
            <button
              onClick={() => window.electronAPI.goBack()}
              className="text-gray-400 hover:text-white text-sm underline"
            >
              ← Back to Queue
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
