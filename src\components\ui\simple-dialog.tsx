// Simple dialog replacement for Radix UI
import * as React from "react"
import { cn } from "../../lib/utils"

interface DialogContextType {
  open: boolean
  setOpen: (open: boolean) => void
}

const DialogContext = React.createContext<DialogContextType | undefined>(undefined)

export const Dialog: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [open, setOpen] = React.useState(false)
  
  return (
    <DialogContext.Provider value={{ open, setOpen }}>
      {children}
    </DialogContext.Provider>
  )
}

export const DialogTrigger: React.FC<{ 
  children: React.ReactNode
  asChild?: boolean
}> = ({ children, asChild = false }) => {
  const context = React.useContext(DialogContext)
  if (!context) throw new Error('DialogTrigger must be used within Dialog')
  
  const handleClick = () => context.setOpen(true)
  
  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children, { onClick: handleClick })
  }
  
  return (
    <button onClick={handleClick}>
      {children}
    </button>
  )
}

export const DialogContent: React.FC<{
  children: React.ReactNode
  className?: string
}> = ({ children, className }) => {
  const context = React.useContext(DialogContext)
  if (!context) throw new Error('DialogContent must be used within Dialog')
  
  if (!context.open) return null
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Overlay */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50"
        onClick={() => context.setOpen(false)}
      />
      
      {/* Content */}
      <div className={cn(
        "relative bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4",
        className
      )}>
        {children}
      </div>
    </div>
  )
}

export const DialogClose: React.FC<{
  children: React.ReactNode
  asChild?: boolean
}> = ({ children, asChild = false }) => {
  const context = React.useContext(DialogContext)
  if (!context) throw new Error('DialogClose must be used within Dialog')
  
  const handleClick = () => context.setOpen(false)
  
  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children, { onClick: handleClick })
  }
  
  return (
    <button onClick={handleClick}>
      {children}
    </button>
  )
}
