// src/lib/utils.ts - Simple replacements for clsx and tailwind-merge

type ClassValue = string | number | boolean | undefined | null | ClassValue[]

// Simple clsx replacement
function clsx(...inputs: ClassValue[]): string {
  const classes: string[] = []

  inputs.forEach(input => {
    if (!input) return

    if (typeof input === 'string' || typeof input === 'number') {
      classes.push(String(input))
    } else if (Array.isArray(input)) {
      const result = clsx(...input)
      if (result) classes.push(result)
    } else if (typeof input === 'object') {
      Object.entries(input).forEach(([key, value]) => {
        if (value) classes.push(key)
      })
    }
  })

  return classes.join(' ')
}

// Simple tailwind merge replacement (just removes duplicates)
function twMerge(classNames: string): string {
  return classNames
    .split(' ')
    .filter((className, index, array) => {
      // Keep the last occurrence of each class
      return array.lastIndexOf(className) === index && className.trim()
    })
    .join(' ')
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
