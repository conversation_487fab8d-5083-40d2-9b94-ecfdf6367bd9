// Simple logger to replace electron-log
class SimpleLogger {
  transports = {
    file: {
      level: "debug"
    }
  }

  info(...args: any[]) {
    console.log('[INFO]', ...args)
  }

  error(...args: any[]) {
    console.error('[ERROR]', ...args)
  }

  warn(...args: any[]) {
    console.warn('[WARN]', ...args)
  }

  debug(...args: any[]) {
    console.debug('[DEBUG]', ...args)
  }
}

const log = new SimpleLogger()
export default log
