// Simple store implementation to replace electron-store
import fs from 'fs'
import path from 'path'
import { app } from 'electron'

interface StoreSchema {
  geminiApiKey?: string
}

class SimpleStore {
  private data: StoreSchema = {}
  private filePath: string

  constructor() {
    const userDataPath = app.getPath('userData')
    this.filePath = path.join(userDataPath, 'config.json')
    this.load()
  }

  private load() {
    try {
      if (fs.existsSync(this.filePath)) {
        const fileContent = fs.readFileSync(this.filePath, 'utf8')
        this.data = JSON.parse(fileContent)
      }
    } catch (error) {
      console.error('Error loading store:', error)
      this.data = {}
    }
  }

  private save() {
    try {
      const dir = path.dirname(this.filePath)
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
      }
      fs.writeFileSync(this.filePath, JSON.stringify(this.data, null, 2))
    } catch (error) {
      console.error('Error saving store:', error)
    }
  }

  get(key: keyof StoreSchema): any {
    return this.data[key]
  }

  set(key: keyof StoreSchema, value: any) {
    this.data[key] = value
    this.save()
  }
}

export const store = new SimpleStore()
