import SubscribedApp from "./_pages/SubscribedApp"
import Settings from "./_pages/Settings"
import { UpdateNotification } from "./components/UpdateNotification"
import { useEffect, useState, useCallback } from "react"
import {
  ToastProvider,
  useToast as useSimpleToast
} from "./components/ui/simple-toast"
import { ToastContext } from "./contexts/toast"

// Root component for the free version
function App() {
  return (
    <ToastProvider>
      <AppContent />
    </ToastProvider>
  )
}

function AppContent() {
  const [currentLanguage, setCurrentLanguage] = useState<string>("python")
  const [isInitialized, setIsInitialized] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const { addToast } = useSimpleToast()

  // Helper function to safely update language
  const updateLanguage = useCallback((newLanguage: string) => {
    setCurrentLanguage(newLanguage)
    window.__LANGUAGE__ = newLanguage
  }, [])

  // Helper function to mark initialization complete
  const markInitialized = useCallback(() => {
    setIsInitialized(true)
    window.__IS_INITIALIZED__ = true
  }, [])

  // Show toast method using our simple toast system
  const showToast = useCallback(
    (
      title: string,
      description: string,
      variant: "default" | "destructive" | "success" | "info"
    ) => {
      addToast({
        title,
        description,
        variant,
        duration: 3000
      })
    },
    [addToast]
  )

  // Simple initialization
  useEffect(() => {
    // Only set default language if not already set
    if (!window.__LANGUAGE__) {
      updateLanguage("python")
    }
    markInitialized()
  }, [updateLanguage, markInitialized])

  return (
    <ToastContext.Provider value={{ showToast }}>
      {showSettings ? (
        <Settings />
      ) : (
        <SubscribedApp
          isInitialized={isInitialized}
          onShowSettings={() => setShowSettings(true)}
        />
      )}
      <UpdateNotification />
    </ToastContext.Provider>
  )
}

export default App
