// GeminiService.ts
import { GoogleGenerativeAI } from "./GoogleGenerativeAI"
import fs from "node:fs"
import path from "node:path"

export interface ProblemInfo {
  title: string
  description: string
  examples: string[]
  constraints: string[]
  difficulty: string
}

export interface Solution {
  code: string
  explanation: string
  timeComplexity: string
  spaceComplexity: string
}

export interface DebugResult {
  issues: string[]
  suggestions: string[]
  fixedCode?: string
}

export class GeminiService {
  private genAI: GoogleGenerativeAI | null = null
  private model: any = null

  constructor(apiKey?: string) {
    if (apiKey) {
      this.initialize(apiKey)
    }
  }

  public initialize(apiKey: string): void {
    try {
      this.genAI = new GoogleGenerativeAI(apiKey)
      this.model = this.genAI.getGenerativeModel({ model: "gemini-1.5-flash" })
    } catch (error) {
      console.error("Failed to initialize Gemini:", error)
      throw new Error("Failed to initialize Gemini API")
    }
  }

  public isInitialized(): boolean {
    return this.genAI !== null && this.model !== null
  }

  private async convertImageToBase64(imagePath: string): Promise<string> {
    try {
      const imageBuffer = fs.readFileSync(imagePath)
      return imageBuffer.toString('base64')
    } catch (error) {
      console.error("Error reading image file:", error)
      throw new Error("Failed to read image file")
    }
  }

  private getImageMimeType(imagePath: string): string {
    const ext = path.extname(imagePath).toLowerCase()
    switch (ext) {
      case '.png':
        return 'image/png'
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg'
      case '.webp':
        return 'image/webp'
      default:
        return 'image/png'
    }
  }

  public async extractProblemInfo(imageDataList: string[], language: string): Promise<ProblemInfo> {
    if (!this.isInitialized()) {
      throw new Error("Gemini service not initialized")
    }

    try {
      const imageParts = imageDataList.map((imageData, index) => {
        // If imageData is a file path, convert to base64
        if (imageData.startsWith('/') || imageData.includes('\\') || imageData.includes(':')) {
          const base64Data = this.convertImageToBase64(imageData)
          const mimeType = this.getImageMimeType(imageData)
          return {
            inlineData: {
              data: base64Data,
              mimeType: mimeType
            }
          }
        } else {
          // Assume it's already base64 data
          return {
            inlineData: {
              data: imageData,
              mimeType: 'image/png'
            }
          }
        }
      })

      const prompt = `
        Analyze the coding interview problem shown in these screenshots. Extract the following information:

        1. Problem title
        2. Problem description
        3. Examples with input/output
        4. Constraints
        5. Difficulty level

        Please provide the response in the following JSON format:
        {
          "title": "Problem Title",
          "description": "Detailed problem description",
          "examples": ["Example 1: Input: ... Output: ...", "Example 2: ..."],
          "constraints": ["Constraint 1", "Constraint 2"],
          "difficulty": "Easy/Medium/Hard"
        }

        Focus on extracting the exact text from the problem statement. If you can't see certain parts clearly, indicate that in the response.
      `

      const result = await this.model.generateContent([prompt, ...imageParts])
      const response = await result.response
      const text = response.text()

      // Try to parse JSON from the response
      try {
        const jsonMatch = text.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          const problemInfo = JSON.parse(jsonMatch[0])
          return problemInfo
        }
      } catch (parseError) {
        console.error("Failed to parse JSON response:", parseError)
      }

      // Fallback: create a basic structure from the text
      return {
        title: "Extracted Problem",
        description: text,
        examples: [],
        constraints: [],
        difficulty: "Unknown"
      }

    } catch (error) {
      console.error("Error extracting problem info:", error)
      throw new Error("Failed to extract problem information from images")
    }
  }

  public async generateSolution(problemInfo: ProblemInfo, language: string): Promise<Solution> {
    if (!this.isInitialized()) {
      throw new Error("Gemini service not initialized")
    }

    try {
      const prompt = `
        You are an expert coding interview assistant. Given the following problem, provide a complete solution in ${language}.

        Problem Title: ${problemInfo.title}
        Description: ${problemInfo.description}
        Examples: ${problemInfo.examples.join('\n')}
        Constraints: ${problemInfo.constraints.join('\n')}
        Difficulty: ${problemInfo.difficulty}

        Please provide:
        1. A clean, well-commented solution in ${language}
        2. A clear explanation of the approach
        3. Time complexity analysis
        4. Space complexity analysis

        Format your response as JSON:
        {
          "code": "// Your solution code here",
          "explanation": "Step-by-step explanation of the approach",
          "timeComplexity": "O(n) - explanation",
          "spaceComplexity": "O(1) - explanation"
        }

        Make sure the code is production-ready and follows best practices for ${language}.
      `

      const result = await this.model.generateContent(prompt)
      const response = await result.response
      const text = response.text()

      // Try to parse JSON from the response
      try {
        const jsonMatch = text.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          const solution = JSON.parse(jsonMatch[0])
          return solution
        }
      } catch (parseError) {
        console.error("Failed to parse JSON response:", parseError)
      }

      // Fallback: create a basic structure from the text
      return {
        code: text,
        explanation: "Solution generated by Gemini",
        timeComplexity: "Analysis not available",
        spaceComplexity: "Analysis not available"
      }

    } catch (error) {
      console.error("Error generating solution:", error)
      throw new Error("Failed to generate solution")
    }
  }

  public async debugCode(imageDataList: string[], problemInfo: ProblemInfo, language: string): Promise<DebugResult> {
    if (!this.isInitialized()) {
      throw new Error("Gemini service not initialized")
    }

    try {
      const imageParts = imageDataList.map((imageData, index) => {
        // If imageData is a file path, convert to base64
        if (imageData.startsWith('/') || imageData.includes('\\') || imageData.includes(':')) {
          const base64Data = this.convertImageToBase64(imageData)
          const mimeType = this.getImageMimeType(imageData)
          return {
            inlineData: {
              data: base64Data,
              mimeType: mimeType
            }
          }
        } else {
          // Assume it's already base64 data
          return {
            inlineData: {
              data: imageData,
              mimeType: 'image/png'
            }
          }
        }
      })

      const prompt = `
        You are an expert code reviewer. Analyze the code shown in these screenshots for the following problem:

        Problem: ${problemInfo.title}
        Description: ${problemInfo.description}
        Language: ${language}

        Please identify:
        1. Any bugs or logical errors
        2. Performance issues
        3. Code style problems
        4. Suggestions for improvement
        5. If possible, provide a corrected version

        Format your response as JSON:
        {
          "issues": ["Issue 1", "Issue 2"],
          "suggestions": ["Suggestion 1", "Suggestion 2"],
          "fixedCode": "// Corrected code if applicable"
        }

        Be specific and helpful in your analysis.
      `

      const result = await this.model.generateContent([prompt, ...imageParts])
      const response = await result.response
      const text = response.text()

      // Try to parse JSON from the response
      try {
        const jsonMatch = text.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          const debugResult = JSON.parse(jsonMatch[0])
          return debugResult
        }
      } catch (parseError) {
        console.error("Failed to parse JSON response:", parseError)
      }

      // Fallback: create a basic structure from the text
      return {
        issues: ["Analysis completed"],
        suggestions: [text],
        fixedCode: undefined
      }

    } catch (error) {
      console.error("Error debugging code:", error)
      throw new Error("Failed to debug code")
    }
  }
}
