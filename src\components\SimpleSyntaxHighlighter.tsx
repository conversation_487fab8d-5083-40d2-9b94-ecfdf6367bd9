// Simple syntax highlighter replacement
import React from 'react'

interface SimpleSyntaxHighlighterProps {
  children: string
  language?: string
  style?: any
  customStyle?: React.CSSProperties
  wrapLongLines?: boolean
}

export const SimpleSyntaxHighlighter: React.FC<SimpleSyntaxHighlighterProps> = ({
  children,
  language = 'python',
  customStyle = {},
  wrapLongLines = false
}) => {
  const defaultStyle: React.CSSProperties = {
    backgroundColor: '#282a36',
    color: '#f8f8f2',
    padding: '16px',
    borderRadius: '8px',
    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
    fontSize: '14px',
    lineHeight: '1.5',
    overflow: 'auto',
    whiteSpace: wrapLongLines ? 'pre-wrap' : 'pre',
    wordBreak: wrapLongLines ? 'break-word' : 'normal',
    ...customStyle
  }

  // Simple syntax highlighting for common keywords
  const highlightSyntax = (code: string, lang: string) => {
    if (!code) return ''
    
    let highlighted = code
    
    // Python keywords
    if (lang === 'python') {
      const pythonKeywords = [
        'def', 'class', 'if', 'else', 'elif', 'for', 'while', 'try', 'except', 
        'finally', 'with', 'as', 'import', 'from', 'return', 'yield', 'break', 
        'continue', 'pass', 'and', 'or', 'not', 'in', 'is', 'lambda', 'True', 
        'False', 'None'
      ]
      
      pythonKeywords.forEach(keyword => {
        const regex = new RegExp(`\\b${keyword}\\b`, 'g')
        highlighted = highlighted.replace(regex, `<span style="color: #ff79c6">${keyword}</span>`)
      })
      
      // Strings
      highlighted = highlighted.replace(/(["'])((?:\\.|(?!\1)[^\\])*?)\1/g, 
        '<span style="color: #f1fa8c">$1$2$1</span>')
      
      // Comments
      highlighted = highlighted.replace(/(#.*$)/gm, 
        '<span style="color: #6272a4">$1</span>')
      
      // Numbers
      highlighted = highlighted.replace(/\b\d+\.?\d*\b/g, 
        '<span style="color: #bd93f9">$&</span>')
    }
    
    // JavaScript keywords
    if (lang === 'javascript' || lang === 'js') {
      const jsKeywords = [
        'function', 'const', 'let', 'var', 'if', 'else', 'for', 'while', 'do',
        'switch', 'case', 'default', 'break', 'continue', 'return', 'try', 'catch',
        'finally', 'throw', 'new', 'this', 'typeof', 'instanceof', 'true', 'false',
        'null', 'undefined'
      ]
      
      jsKeywords.forEach(keyword => {
        const regex = new RegExp(`\\b${keyword}\\b`, 'g')
        highlighted = highlighted.replace(regex, `<span style="color: #ff79c6">${keyword}</span>`)
      })
    }
    
    return highlighted
  }

  const highlightedCode = highlightSyntax(children, language)

  return (
    <pre style={defaultStyle}>
      <code dangerouslySetInnerHTML={{ __html: highlightedCode }} />
    </pre>
  )
}

// Export as both named and default for compatibility
export default SimpleSyntaxHighlighter

// Export Prism for compatibility with existing imports
export const Prism = {
  SyntaxHighlighter: SimpleSyntaxHighlighter
}
