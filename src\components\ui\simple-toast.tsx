// Simple toast replacement for Radix UI
import * as React from "react"
import { AlertCircle, CheckCircle2, Info, X } from "lucide-react"

// Simple toast context and provider
interface ToastContextType {
  toasts: Toast[]
  addToast: (toast: Omit<Toast, 'id'>) => void
  removeToast: (id: string) => void
}

interface Toast {
  id: string
  title?: string
  description?: string
  variant?: 'default' | 'destructive' | 'success' | 'info'
  duration?: number
}

const ToastContext = React.createContext<ToastContextType | undefined>(undefined)

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = React.useState<Toast[]>([])

  const addToast = React.useCallback((toast: Omit<Toast, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9)
    const newToast = { ...toast, id }
    setToasts(prev => [...prev, newToast])

    // Auto remove after duration
    setTimeout(() => {
      setToasts(prev => prev.filter(t => t.id !== id))
    }, toast.duration || 3000)
  }, [])

  const removeToast = React.useCallback((id: string) => {
    setToasts(prev => prev.filter(t => t.id !== id))
  }, [])

  return (
    <ToastContext.Provider value={{ toasts, addToast, removeToast }}>
      {children}
      <ToastViewport />
    </ToastContext.Provider>
  )
}

export const useToast = () => {
  const context = React.useContext(ToastContext)
  if (!context) {
    throw new Error('useToast must be used within ToastProvider')
  }
  return context
}

// Toast components
export const Toast: React.FC<{
  open?: boolean
  onOpenChange?: (open: boolean) => void
  variant?: 'default' | 'destructive' | 'success' | 'info'
  duration?: number
  children: React.ReactNode
}> = ({ children, variant = 'default' }) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'destructive':
        return 'bg-red-600 text-white border-red-600'
      case 'success':
        return 'bg-green-600 text-white border-green-600'
      case 'info':
        return 'bg-blue-600 text-white border-blue-600'
      default:
        return 'bg-gray-800 text-white border-gray-700'
    }
  }

  return (
    <div className={`rounded-lg border p-4 shadow-lg ${getVariantStyles()}`}>
      {children}
    </div>
  )
}

export const ToastTitle: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="font-semibold text-sm">{children}</div>
)

export const ToastDescription: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="text-sm opacity-90 mt-1">{children}</div>
)

export const ToastViewport: React.FC = () => {
  const { toasts, removeToast } = useToast()

  if (toasts.length === 0) return null

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {toasts.map((toast) => (
        <div
          key={toast.id}
          className={`rounded-lg border p-4 shadow-lg min-w-[300px] ${getToastVariantStyles(toast.variant)}`}
        >
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-2">
              {getToastIcon(toast.variant)}
              <div>
                {toast.title && (
                  <div className="font-semibold text-sm">{toast.title}</div>
                )}
                {toast.description && (
                  <div className="text-sm opacity-90 mt-1">{toast.description}</div>
                )}
              </div>
            </div>
            <button
              onClick={() => removeToast(toast.id)}
              className="text-white/70 hover:text-white"
            >
              <X size={16} />
            </button>
          </div>
        </div>
      ))}
    </div>
  )
}

const getToastVariantStyles = (variant?: string) => {
  switch (variant) {
    case 'destructive':
      return 'bg-red-600 text-white border-red-600'
    case 'success':
      return 'bg-green-600 text-white border-green-600'
    case 'info':
      return 'bg-blue-600 text-white border-blue-600'
    default:
      return 'bg-gray-800 text-white border-gray-700'
  }
}

const getToastIcon = (variant?: string) => {
  switch (variant) {
    case 'destructive':
      return <AlertCircle size={16} className="text-red-200 mt-0.5" />
    case 'success':
      return <CheckCircle2 size={16} className="text-green-200 mt-0.5" />
    case 'info':
      return <Info size={16} className="text-blue-200 mt-0.5" />
    default:
      return <Info size={16} className="text-gray-200 mt-0.5" />
  }
}
