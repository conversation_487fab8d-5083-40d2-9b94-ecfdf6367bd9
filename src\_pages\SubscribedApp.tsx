// file: src/components/SubscribedApp.tsx
import { useEffect, useRef, useState } from "react"
import Queue from "../_pages/Queue"
import Solutions from "../_pages/Solutions"
import { useToast } from "../contexts/toast"

interface SubscribedAppProps {
  isInitialized: boolean
  onShowSettings: () => void
}

const SubscribedApp: React.FC<SubscribedAppProps> = ({
  isInitialized,
  onShowSettings
}) => {
  const [view, setView] = useState<"queue" | "solutions" | "debug">("queue")
  const [currentLanguage, setCurrentLanguage] = useState<string>(() => {
    // Initialize from global state or default to python
    return window.__LANGUAGE__ || "python"
  })
  const containerRef = useRef<HTMLDivElement>(null)
  const { showToast } = useToast()

  // Sync language changes with global state
  const handleLanguageChange = (newLanguage: string) => {
    setCurrentLanguage(newLanguage)
    window.__LANGUAGE__ = newLanguage
    console.log("SubscribedApp: Language changed to", newLanguage)
  }

  // Handle reset view events
  useEffect(() => {
    const cleanup = window.electronAPI.onResetView(() => {
      setView("queue")
    })

    return () => {
      cleanup()
    }
  }, [])

  // Dynamically update the window size
  useEffect(() => {
    if (!containerRef.current) return

    const updateDimensions = () => {
      if (!containerRef.current) return
      const height = containerRef.current.scrollHeight
      const width = containerRef.current.scrollWidth
      window.electronAPI?.updateContentDimensions({ width, height })
    }

    const resizeObserver = new ResizeObserver(updateDimensions)
    resizeObserver.observe(containerRef.current)

    // Also watch DOM changes
    const mutationObserver = new MutationObserver(updateDimensions)
    mutationObserver.observe(containerRef.current, {
      childList: true,
      subtree: true,
      attributes: true,
      characterData: true
    })

    // Initial dimension update
    updateDimensions()

    return () => {
      resizeObserver.disconnect()
      mutationObserver.disconnect()
    }
  }, [view])

  // Listen for events that might switch views or show errors
  useEffect(() => {
    const cleanupFunctions = [
      window.electronAPI.onSolutionStart(() => {
        setView("solutions")
      }),
      window.electronAPI.onUnauthorized(() => {
        setView("queue")
      }),
      window.electronAPI.onResetView(() => {
        setView("queue")
      }),
      window.electronAPI.onProblemExtracted((data: any) => {
        // Problem extracted, data is handled by the components
      }),
      window.electronAPI.onSolutionError((error: string) => {
        showToast("Error", error, "destructive")
      })
    ]
    return () => cleanupFunctions.forEach((fn) => fn())
  }, [view])

  return (
    <div ref={containerRef} className="min-h-0">
      {view === "queue" ? (
        <Queue
          setView={setView}
          currentLanguage={currentLanguage}
          setLanguage={handleLanguageChange}
          onShowSettings={onShowSettings}
        />
      ) : view === "solutions" ? (
        <Solutions
          setView={setView}
          currentLanguage={currentLanguage}
          setLanguage={handleLanguageChange}
        />
      ) : null}
    </div>
  )
}

export default SubscribedApp
